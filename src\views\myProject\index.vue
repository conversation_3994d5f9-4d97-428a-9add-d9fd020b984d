<script setup lang="ts">
import FormSwitch from "@/views/common/formSwitch.vue";
import RecordsDrawer from "@/views/common/RecordsDrawer.vue";
import {
  prjForm,
  prjState,
  prjType,
  typeDict,
  stateDict,
  formDict,
  conditionState,
} from "@/utils/constant";
import { nextTick, onMounted, ref, watch, watchEffect } from "vue";
import MyButton from "@/views/common/myButton.vue";
import {
  type key2prjList,
  getProjectList,
  deleteProject,
  withdrawProject,
  publishProject,
} from "@/apis/path/myProject";
import { ElMessage, ElMessageBox } from "element-plus";
import { useRouter } from "vue-router";
import { decodeData } from "@/utils/func";
import type { prjInfo4table } from "@/utils/type";
import MyFlipper from "@/views/common/myFlipper.vue";

import { editingPrjStore } from "@/stores/editingPrj";
import { renderMarkdown } from "@/utils/markdown";

const router = useRouter();
const drawerRef = ref();
const prjTable = ref();
const curForm = ref("");
const curState = ref(prjState.default.toString());
const curType = ref(prjType.default.toString());
const tableData = ref([]);
const searchKey = ref("");
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const multipleSelection = ref<prjInfo4table[]>([]);
const handleSelect = (form: prjForm) => {
  if (form !== prjForm.all) {
    curForm.value = form.toString();
  } else {
    curForm.value = "";
  }
  getPrjList();
};

const handleSearch = (key: string) => {
  searchKey.value = key;
  getPrjList();
};
const getPrjList = () => {
  const param = ref<key2prjList>({
    current: currentPage.value,
    limit: pageSize.value,
    prjForm: curForm.value,
    prjType: curType.value,
    status: curState.value,
    title: searchKey.value,
  });
  // console.log("[param]",param.value)
  // TODO: param.prjForm === 3 返回全部项目
  getProjectList(param.value)
    .then((res) => {
      if (res.success) {
        tableData.value = res.data.list?.map((prj: prjInfo4table) =>
          decodeData(prj)
        );
        total.value = res.data.total;
      } else {
        ElMessage.error(res.message);
      }
    })
    .catch();
};
// 处理查看审核记录
const handleCheckRecords = (data: any) => {
  const item = {
    id: data.id,
    title: data.prname,
  };
  drawerRef.value.showDrawer(item);
};

const handleDelete = (id?: number) => {
  let idList = [];
  if (id) {
    idList.push(id);
  } else {
    idList = multipleSelection.value?.map((prj: prjInfo4table) => prj.id);
  }
  if (!id && multipleSelection.value.length == 0) {
    ElMessage.error("请选择项目");
  } else {
    ElMessageBox.confirm("是否确定删除？", "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    })
      .then(() => {
        deleteProject(idList)
          .then((res) => {
            if (res.success) {
              ElMessage.success("成功删除");
              getPrjList();
            } else {
              ElMessage.error("删除失败");
            }
          })
          .catch();
        getPrjList();
      })
      .catch();
  }
};
const handleWithdraw = (id?: number) => {
  let idList = [];
  if (id) {
    idList.push(id);
  } else {
    console.log("没有正确传入需要撤回的项目");
    return;
    // 撤回没有批量功能
    // idList = prjTable.value.selection.map(prj => prj.id);
  }
  withdrawProject(idList)
    .then((res) => {
      if (res.success) {
        ElMessage.success("成功撤回");
        getPrjList();
      } else {
        ElMessage.error("撤回失败");
      }
    })
    .catch();
};
const ToPreview = (id: number, form: string) => {
  const prjFrom = formDict[form];
  const { href } = router.resolve({
    path: "/home/<USER>",
    query: {
      prjId: id,
      prjForm: prjFrom,
    },
  });
  window.open(href, "_self");
};
const ToEdit = (id: number, form: string) => {
  const prjFrom = formDict[form];
  // TODO：添加行为逻辑后，这个地方可能要动态判断跳往步骤x
  const { href } = router.resolve({
    path: "/home/<USER>/step1",
    query: {
      prjId: id,
      prjForm: prjFrom,
    },
  });
  window.open(href, "_blank");
};
const handleChangePage = (newPage: number) => {
  currentPage.value = newPage;
  getPrjList();
};
const handleSelectionChange = (val: prjInfo4table[]) => {
  multipleSelection.value = val;
};
const indexMethod = (index: number) => {
  return (currentPage.value - 1) * pageSize.value + index + 1;
};

const setCur = ($event: any) => {
  curState.value = $event.curState;
  curType.value = $event.curType;
  getPrjList();
};

onMounted(() => {
  getPrjList();
  document.addEventListener("visibilitychange", () => {
    if (!document["hidden"]) {
      getPrjList();
      // console.log('出现');
    } else {
      //隐藏
      // console.log('隐藏');
    }
  });
});
// watch(()=>localStorage.getItem('refreshHomeTable'), (newVal) => {
//   if (newVal == '1') {
//     alert('aaa')
//     getPrjList();
//     localStorage.setItem('refreshHomeTable', '0');
//   }
// }, {deep:true, immediate:true})
</script>

<template>
  <div class="main-container">
    <form-switch
      :needCurState="true"
      @select="handleSelect"
      @search="handleSearch"
      @cur="setCur($event)"
      placeholder="请输入项目标题"
    ></form-switch>
    <div class="content-container">
      <div class="toolbar">
        <my-button type="primary" @click="handleDelete()">批量删除</my-button>
      </div>
      <div class="line"></div>
      <el-table
        ref="prjTable"
        :data="tableData"
        @selection-change="handleSelectionChange"
        style="width: 100%"
        empty-text="暂无数据"
      >
        <el-table-column
          type="selection"
          width="50"
          :selectable="
            (row) =>
              row.conditionId == conditionState.draft ||
              row.conditionId == conditionState.backbyme ||
              row.conditionId == conditionState.backbyother
          "
        ></el-table-column>
        <el-table-column
          type="index"
          :index="indexMethod"
          label="序号"
          width="75"
        >
        </el-table-column>
        <el-table-column
          class="notFixWidth"
          prop="prname"
          label="项目名称"
          width="258"
        >
          <template #default="scope">
            <span style="width: 100%; justify-content: flex-start">
              <span
                class="prname ck-content"
                v-html="renderMarkdown(scope.row.prname)"
              ></span>
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="prform" label="项目形式" width="100">
        </el-table-column>
        <el-table-column
          prop="prtype"
          label="项目类型"
          width="143"
        ></el-table-column>
        <el-table-column
          prop="oprtime"
          label="操作时间"
          width="211"
        ></el-table-column>
        <el-table-column
          prop="condition"
          label="状态"
          width="123"
        ></el-table-column>
        <el-table-column label="操作" width="180">
          <template #default="scope">
            <span class="operationBlock">
              <!-- 使用枚举类可读性更高 -->
              <span
                v-if="
                  scope.row.conditionId == conditionState.draft ||
                  scope.row.conditionId == conditionState.backbyme ||
                  scope.row.conditionId == conditionState.backbyother
                "
                @click="ToEdit(scope.row.id, scope.row.prform)"
                class="operationBtn"
                >编辑</span
              >
              <span
                v-else
                @click="ToPreview(scope.row.id, scope.row.prform)"
                class="operationBtn"
                >预览</span
              >
              <span
                v-if="
                  scope.row.conditionId == conditionState.online ||
                  scope.row.conditionId == conditionState.waitingpass
                "
                @click="handleWithdraw(scope.row.id)"
                class="operationBtn"
                >撤回</span
              >
              <span
                v-else-if="scope.row.conditionId == conditionState.nowpassing"
                class="operationBtn2"
                >删除</span
              >
              <span
                v-else
                @click="handleDelete(scope.row.id)"
                class="operationBtn"
                >删除</span
              >
              <span @click="handleCheckRecords(scope.row)" class="operationBtn"
                >审核记录</span
              >
            </span>
          </template>
        </el-table-column>
      </el-table>
      <my-flipper
        @change-page="handleChangePage"
        :current="currentPage"
        :page-size="pageSize"
        :total="total"
      ></my-flipper>
    </div>
  </div>
  <!-- 其他 -->
  <records-drawer ref="drawerRef"></records-drawer>
</template>

<style scoped>
.main-container {
  font-family: var(--font-family-text);
  color: var(--color-black);
  width: var(--width-content);
  /* margin: 20px auto auto auto; */

  .content-container {
    display: flex;
    flex-direction: column;
    width: 100%;
    padding: 5px 30px 30px 30px;
    background-color: white;

    .toolbar {
      width: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;
      flex-direction: row;
      padding-bottom: 5px;

      .select-group {
        width: 390px;
        display: flex;
        justify-content: space-between;

        .select {
          /*--el-color-primary: var(--color-primary);*/
          width: 180px;

          &:deep(.el-input) {
            --el-input-height: 35px;
            line-height: 35px;
          }
        }
      }

      /* &::after {
        content: "";
        height: 1px;
        width: 100%;
        background-color: var(--color-line);
        position: absolute;
        bottom: 0;
      } */
    }

    .el-table {
      &:deep(.el-table__cell) {
        padding: 0;
        height: 55px;
      }

      &:deep(.cell) {
        justify-content: center;
        display: flex;
        align-items: center;
        height: 55px;
      }
    }

    .operationBlock {
      display: flex;
      width: 150px;
      justify-content: space-between;
      align-items: center;
      flex-direction: row;

      .operationBtn {
        color: var(--color-primary);
        &:hover {
          cursor: pointer;
          color: var(--color-primary);
          font-weight: 600;
          /* text-decoration: underline; */
        }
      }

      .operationBtn2 {
        color: #f2f2f2;
        /*
        &:hover {
          cursor: pointer;
          color: var(--color-primary);
          text-decoration: underline;
        } */
      }
    }
  }
}
</style>
