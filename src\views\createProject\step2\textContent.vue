<script setup lang="ts">
// import classicCKEditor from "@/views/common/classicCKEditor.vue";
import ClassicEditor from "@/components/editors/Vditor.vue";
import {
  convertLongUrlMarkdownImages,
  convertImgTagLongUrls,
  convertLanguageMathToScript,
} from "@/utils/latexUtils";
import { markdownToHtml, encodeHTML } from "@/utils/mdTohtml";
import { inject, reactive, type Ref, ref, watch } from "vue";
import {
  getTextSection,
  type params4submitTextSection,
  submitTextSection,
} from "@/apis/path/createProject";
import { ElMessage, type FormInstance, type FormRules } from "element-plus";
const editorRef = ref();

// 本组件进行前后端通信
const emits = defineEmits([
  "sendSecName",
  "saveSectionDraftDone",
  "saveSectionDone",
]);
const props = defineProps({
  projectId: Number,
  sectionId: Number,
  sectionCount: Number,
  sectionName: String,
});
const prjId = ref();
const secId = ref();
const secCount = ref();
const secName = ref();
const ruleFormRef = ref<FormInstance>();
const ruleForm = reactive({
  title: "",
  content: "",
});
const rules = reactive<FormRules<typeof ruleForm>>({
  content: [{ required: true, message: "请输入内容", trigger: "blur" }],
});
const initData = () => {
  if (secId.value) {
    getTextSection(secId.value)
      .then((res) => {
        if (res.success) {
          const textSection = res.data.wordsContent.projectSections[0];
          secName.value = textSection.sectionTitle;
          ruleForm.content = textSection.prText;
          emits("sendSecName", secName.value);
        } else {
          ElMessage.error(res.message);
        }
      })
      .catch();
  }
};

watch(
  () => props,
  (newValue, oldValue) => {
    prjId.value = newValue.projectId;
    secId.value = newValue.sectionId;
    secCount.value = newValue.sectionCount;
    // 检查 sectionId 是否为 -1，如果是，则不执行 initData
    if (secId.value == -1) {
      return;
    }
    // 执行初始化数据的逻辑
    initData();
  },
  { deep: true, immediate: true }
);
const setSectionName = (newSecName: string) => {
  secName.value = newSecName;
};
const handleSaveDraftSection = () => {
  if (
    prjId.value == undefined ||
    secId.value == undefined ||
    secCount.value == undefined
  )
    return;
  const param: params4submitTextSection = {
    projectId: prjId.value,
    projectSections: [
      {
        sectionId: secId.value,
        sectionNum: secCount.value,
        sectionTitle: secName.value,
        prText: convertLongUrlMarkdownImages(editorRef.value.getData()),
        sectionContent: [],
        htmlContent: convertLanguageMathToScript(
          convertImgTagLongUrls(editorRef.value.getHtml())
        ),
      },
    ],
  };
  submitTextSection(param)
    .then((res) => {
      if (res.success) {
        ElMessage.success("保存成功");
        // 表明本次保存草稿结束，重置父组件中的saveSectionId
        emits("saveSectionDraftDone", true);
      } else {
        ElMessage.error(res.message);
        emits("saveSectionDraftDone", false);
      }
    })
    .catch();
};

const handleSaveSection = () => {
  if (
    prjId.value == undefined ||
    secId.value == undefined ||
    secCount.value == undefined
  )
    return;
  ruleForm.title = secName.value;
  if (!ruleFormRef.value) return;
  ruleFormRef.value.validate((valid) => {
    if (valid) {
      const param: params4submitTextSection = {
        projectId: prjId.value,
        projectSections: [
          {
            sectionId: secId.value,
            sectionNum: secCount.value,
            sectionTitle: secName.value,
            // prText: convertLongUrlMarkdownImages(editorRef.value.getData()),
            prText: convertLanguageMathToScript(
              convertImgTagLongUrls(editorRef.value.getHtml())
            ),
            sectionContent: [],
            // htmlContent: convertLanguageMathToScript(
            //   convertImgTagLongUrls(editorRef.value.getHtml())
            // ),
          },
        ],
      };
      console.log("param:", param);
      submitTextSection(param)
        .then((res) => {
          if (res.success) {
            // ElMessage.success(res.message);
            // 表明本次校验保存结束，父组件中应该跳转到下一步
            emits("saveSectionDone", true);
          } else {
            ElMessage.error(res.message);
            emits("saveSectionDone", false);
          }
        })
        .catch();
    } else {
      emits("saveSectionDone", false);
      return false;
    }
  });
};
const saveDraftSectionId = inject("saveDraftSection") as Ref;
const saveSectionId = inject("saveSection") as Ref;
watch(
  () => saveDraftSectionId,
  (newVal, oldVal) => {
    if (newVal.value != -1 && secId.value == newVal.value) {
      handleSaveDraftSection();
    }
  },
  { deep: true, immediate: true }
);

watch(
  () => saveSectionId,
  (newVal, oldVal) => {
    if (newVal.value != -1 && secId.value == newVal.value) {
      handleSaveSection();
    }
  },
  { deep: true, immediate: true }
);

defineExpose({
  setSectionName,
});
</script>

<template>
  <div class="text-content-wrapper">
    <el-form
      ref="ruleFormRef"
      :model="ruleForm"
      :rules="rules"
      style="width: 100%"
    >
      <el-form-item prop="content" style="width: 100%">
        <div style="width: 100%">
          <ClassicEditor
            v-model="ruleForm.content"
            ref="editorRef"
            style="width: 100%"
          ></ClassicEditor>
        </div>
      </el-form-item>
    </el-form>
  </div>
</template>

<style scoped>
.text-content-wrapper {
  margin-bottom: 20px;
  width: 100%;
}
/*:deep(.ck-editor__editable_inline) {*/
/*  color: blue;*/
/*  min-height: 200px;*/
/*}*/
</style>
