<script setup lang="ts">
import multiContentWrapper from "./multiContentWrapper.vue";
import singleContentWrapper from "./singleContentWrapper.vue";
import myTag from "@/views/common/myTag.vue";
import MySteps from "@/views/createProject/components/mySteps.vue";
import MyButton from "@/views/common/myButton.vue";
import PrjInfo from "@/views/common/prjInfo.vue";
import addTDialog from "../step2/addTDialog.vue";
import TitleForm from "@/views/createProject/components/titleForm.vue";

import {
  onBeforeMount,
  onMounted,
  onUnmounted,
  provide,
  ref,
  type Ref,
} from "vue";
import { editingPrjStore } from "@/stores/editingPrj";
import {
  getProjectDetail,
  saveDraftAPI,
  type params4saveDraft,
} from "@/apis/path/createProject";
import { formatData_step1, findKeyByValue } from "@/utils/func";
import type {
  prjInfoType,
  tagType,
  simpleChapterInfo,
  taskType,
} from "@/utils/type";
import { Event } from "@/types/event";
import { ElMessage, ElMessageBox, type FormInstance } from "element-plus";
import { useRoute, useRouter } from "vue-router";
import { submitProject } from "@/apis/path/createProject";
import { prjForm, prjType, typeDict } from "@/utils/constant";
import { emitter } from "@/utils/emitter";

const formRef = ref<FormInstance>();
const route = useRoute();
const router = useRouter();
const editingPrj = editingPrjStore();
const curPrjForm = ref(1);
const prjId = ref();
const prjRlsDate = ref();
const curChapterId = ref();
const taskList = ref<taskType[]>([]);
const dialogRef = ref();
// 提交项目：0 ; 增加小节：1 ; 折叠小节：2

provide("taskList", taskList as Ref<taskType[]>);

const prjInfoData = ref<prjInfoType>({
  disable: true,
  prjType: prjType.exam,
  prjName: "",
  prjAim: "",
  prjGeneral: "",
  prjTagList: [] as tagType[],
  prjTargetList: [] as tagType[],
  prjAreaList: [] as tagType[],
  prjCover: {
    commUrl: "",
    echoUrl: "",
  },
});
const chapterList = ref<simpleChapterInfo[]>([]);

// 添加领域
const handleAddArea = () => {
  dialogRef.value.showDialog("area", 1, 1);
};

// 删除领域
const handleDeleteArea = (aimId: string) => {
  // 维护editingPrj_AreaList
  editingPrj.removeArea(aimId);
  prjInfoData.value.prjAreaList = [...editingPrj.getPrjAreaList()];
};

// 添加目标
const handleAddTarget = () => {
  dialogRef.value.showDialog("klg", 1);
};

// 删除目标
const handleDeleteTarget = (aimId: string) => {
  // 维护editingPrj_TargetList
  editingPrj.removeTarget(aimId);
  prjInfoData.value.prjTargetList = [...editingPrj.getPrjTargetList()];
};

// 刷新列表
const refreshTList = (tList: tagType[], tForm: string) => {
  if (tForm == "tag") {
    // editingPrj.setPrjTagList(tList);
    // formData.prjTagList = [];
    // formData.prjTagList.push(...editingPrj.getPrjTagList());
  } else if (tForm == "klg") {
    editingPrj.setPrjTargetList(tList);
    prjInfoData.value.prjTargetList = [];
    prjInfoData.value.prjTargetList.push(...editingPrj.getPrjTargetList());
  } else if (tForm == "area") {
    editingPrj.setPrjAreaList(tList);
    prjInfoData.value.prjAreaList = [];
    prjInfoData.value.prjAreaList.push(...editingPrj.getPrjAreaList());
  }
};

// 获取项目细节
const getProject = () => {
  getProjectDetail(prjId.value).then((res: any) => {
    if (res.success) {
      let baseData = res.data.list[0];
      prjInfoData.value = formatData_step1(baseData);
      prjRlsDate.value = baseData.createTime;
      curPrjForm.value = res.data.list[0].prjFrom;
      chapterList.value = res.data.sectionList;
    } else {
      ElMessage.error("Step2项目信息获取失败");
      //   router.push(`/home/<USER>
    }
  });
};

// 上一步
const handlePreStep = () => {
  ElMessageBox.confirm("如果没有保存新增内容会丢失", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      router.push({
        path: "/home/<USER>/step1",
        query: {
          prjId: prjId.value,
          prjForm: curPrjForm.value,
        },
      });
    })
    .catch(() => {});
};

// 提交
const handleNextStep = () => {
  for (let i = 0; i < taskList.value.length; i++) {
    if (taskList.value[i].taskProcess < 100) {
      ElMessage.error("有小节视频未完成上传");
      return;
    }
  }
  handleSaveDraft(true);
};

// 存草稿开始
const handleSaveDraft = (submit?: boolean) => {
  emitter.emit(Event.SAVE_DRAFT, submit ?? false);
};
// 存草稿结束
const handleSaveDraftDone = (submit: Number) => {
  const param: params4saveDraft = {
    oid: prjId.value,
    targetKlg: [],
    prjType: findKeyByValue(prjInfoData.value.prjType, typeDict),
    status: 0,
  };

  switch (prjInfoData.value.prjType) {
    case "4":
      param.targetKlg =
        prjInfoData.value.prjAreaList?.map((i: tagType) => i.id) || [];
      break;
    default:
      param.targetKlg =
        prjInfoData.value.prjTargetList?.map((i: tagType) => i.id) || [];
      break;
  }
  saveDraftAPI(param)
    .then((res) => {
      if (res.success) {
        ElMessage.success("保存成功");
        if (submit) {
          ElMessageBox.confirm(
            "提交后项目会进入审核状态，确定提交吗?",
            "提示",
            {
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              type: "warning",
            }
          )
            .then(() => {
              submitProject(prjId.value).then((res) => {
                if (res.success) {
                  window.close()
                  // ElMessage.success("提交成功，先不关闭页面");
                } else {
                  ElMessage.error(res.message);
                }
              });
            })
            .catch(() => {});
        }
      } else {
        ElMessage.error(res.message);
      }
    })
    .catch((error) => {
      ElMessage.error("保存失败");
    });
};
onBeforeMount(() => {
  prjId.value = parseInt(route.query.prjId as string);
  curPrjForm.value = editingPrj.getForm();
});

onMounted(() => {
  getProject();
  emitter.on(Event.SAVE_DRAFT_DONE, handleSaveDraftDone);
});
onUnmounted(() => {
  emitter.off(Event.SAVE_DRAFT_DONE, handleSaveDraftDone);
});
</script>

<template>
  <my-steps :action="1" :form="curPrjForm"></my-steps>
  <div class="main-container">
    <title-form :form="curPrjForm"></title-form>
    <div class="line"></div>
    <div class="content-wrapper">
      <prj-info :info-data="prjInfoData"></prj-info>
      <div style="width: 100%">
        <div class="additional_block" v-show="prjInfoData.prjType !== '2'">
          <el-form :model="prjInfoData" ref="formRef">
            <el-form-item
              v-if="prjInfoData.prjType == '1' || prjInfoData.prjType == '3'"
              required
              :label="(prjInfoData.prjType == '1' ? '讲解' : '测评') + '目标'"
              prop="prjTargetList"
              :rules="[{ required: true, message: '请选择目标' }]"
            >
              <div>
                <my-button @click="handleAddTarget" style="margin-left: 40px"
                  >+ 添加知识</my-button
                >
                <div class="selectedWrapper">
                  <my-tag
                    class="t"
                    v-for="klg in prjInfoData.prjTargetList"
                    :tag-id="klg.id"
                    :key="klg.id"
                    @delete="handleDeleteTarget(klg.id)"
                    type="target"
                  >
                    <el-tooltip
                      popper-class="tooltip-width"
                      :content="klg.name"
                      raw-content
                    >
                      <span
                        class="htmlContent3 ck-content"
                        v-html="klg.name"
                      ></span>
                    </el-tooltip>
                  </my-tag>
                </div>
              </div>
            </el-form-item>
            <el-form-item
              label="讲解目标"
              required
              prop="prjAreaList"
              v-if="prjInfoData.prjType == '4'"
              :rules="[{ required: true, message: '请选择目标' }]"
            >
              <my-button @click="handleAddArea" style="margin-left: 40px"
                >+ 添加领域</my-button
              >
              <div class="selectedWrapper">
                <my-tag
                  class="t"
                  v-for="klg in prjInfoData.prjAreaList"
                  :tag-id="klg.id"
                  :key="klg.id"
                  @delete="handleDeleteArea(klg.id)"
                  type="target"
                >
                  <el-tooltip
                    popper-class="tooltip-width"
                    :content="klg.name"
                    raw-content
                  >
                    <span
                      class="htmlContent3"
                      v-html="klg.name"
                    ></span>
                  </el-tooltip>
                </my-tag>
              </div>
            </el-form-item>
          </el-form>
        </div>
      </div>
      <!-- 多节 -->
      <template v-if="prjInfoData.prjType === prjType.prj">
        <multiContentWrapper
          :prj-id="prjId"
          :cur-prj-form="Number(curPrjForm)"
          :chapter-list="chapterList"
          :release-date="prjRlsDate"
        ></multiContentWrapper>
      </template>
      <!-- 单节 -->
      <template v-else>
        <singleContentWrapper
          :prj-id="prjId"
          :cur-prj-form="Number(curPrjForm)"
          :chapter-list="chapterList"
          :release-date="prjRlsDate"
        ></singleContentWrapper>
      </template>
      <div class="tool-bar">
        <my-button type="light" @click="handlePreStep()">上一步</my-button>
        <my-button :clickable="curChapterId != -1" @click="handleSaveDraft()"
          >存草稿</my-button
        >
        <my-button @click="handleNextStep()">提交</my-button>
      </div>
    </div>
  </div>
  <!-- OTHER -->
  <add-t-dialog ref="dialogRef" @submit="refreshTList"></add-t-dialog>
</template>

<style scoped>
.main-container {
  font-family: var(--font-family-text);
  background-color: white;
  color: var(--color-black);
  width: var(--width-content);
  /* margin: 20px auto auto auto; */
  display: flex;
  flex-direction: column;
  align-items: center;
  .additional_block {
    display: flex;
    flex-direction: column;
    width: 100%;
    padding: 10px;
    background-color: var(--color-light);
    .title {
      font-size: 16px;
      font-weight: 600;
    }
    .selectedWrapper {
      padding-right: 20px;
      display: flex;
      width: 100%;
      align-items: flex-start;
      flex-direction: row;
      margin-left: 40px;
      flex-wrap: wrap;
      margin-top: 10px;
      .t {
        margin-right: 30px;
        margin-bottom: 10px;
      }
    }
  }

  .content-wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    padding: 30px;

    :deep(.el-form-item__label) {
      padding: 0;
    }

    .tool-bar {
      width: 420px;
      display: flex;
      justify-content: space-between;
    }

    .section-wrapper {
      width: 100%;

      :deep(.el-collapse-item__header) {
        height: 100%;
        margin-bottom: 10px;
      }

      .collapse-title {
        display: flex;
        width: 100%;
        justify-content: space-between;
        height: 35px;
        line-height: 35px;
        background-color: var(--color-primary);
        color: white;
        padding: 0 10px;
        cursor: default;

        .btns {
          display: flex;
          width: 88px;
          justify-content: space-between;

          .b {
            cursor: pointer;
          }
        }
      }

      :deep(.el-collapse-item__arrow) {
        display: none;
      }
    }
  }
}
</style>
